<?php
// PHP诊断页面
echo "<h1>PHP诊断信息</h1>";
echo "<p>PHP版本: " . phpversion() . "</p>";
echo "<p>SAPI: " . php_sapi_name() . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>服务器变量</h2>";
echo "<table border='1' style='border-collapse: collapse;'>";
$important_vars = [
    'REQUEST_URI', 'SCRIPT_NAME', 'SCRIPT_FILENAME', 'PATH_INFO', 'PATH_TRANSLATED',
    'QUERY_STRING', 'REQUEST_METHOD', 'HTTP_HOST', 'SERVER_NAME', 'DOCUMENT_ROOT',
    'PHP_SELF', 'REDIRECT_STATUS', 'GATEWAY_INTERFACE'
];

foreach ($important_vars as $var) {
    $value = $_SERVER[$var] ?? 'N/A';
    echo "<tr><td><strong>$var</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
}
echo "</table>";

echo "<h2>PHP配置</h2>";
echo "<table border='1' style='border-collapse: collapse;'>";
$php_configs = [
    'cgi.fix_pathinfo', 'doc_root', 'user_dir', 'enable_dl', 'file_uploads',
    'allow_url_fopen', 'allow_url_include', 'auto_prepend_file', 'auto_append_file'
];

foreach ($php_configs as $config) {
    $value = ini_get($config);
    echo "<tr><td><strong>$config</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
}
echo "</table>";

echo "<h2>文件系统检查</h2>";
$current_dir = __DIR__;
echo "<p>当前目录: $current_dir</p>";
echo "<p>脚本文件: " . __FILE__ . "</p>";
echo "<p>文件存在检查:</p>";
echo "<ul>";

$files_to_check = ['shop.php', 'shop_v2.php', 'api_proxy.php', '.htaccess'];
foreach ($files_to_check as $file) {
    $full_path = $current_dir . DIRECTORY_SEPARATOR . $file;
    if (file_exists($full_path)) {
        $perms = substr(sprintf('%o', fileperms($full_path)), -4);
        $size = filesize($full_path);
        echo "<li>✅ $file (权限: $perms, 大小: $size 字节)</li>";
    } else {
        echo "<li>❌ $file (不存在)</li>";
    }
}
echo "</ul>";

echo "<h2>测试链接</h2>";
echo "<ul>";
echo "<li><a href='shop_v2.php?sj=8034567958'>测试 shop_v2.php</a></li>";
echo "<li><a href='api_proxy.php?action=get_merchant_info&merchant_id=8034567958'>测试 API代理</a></li>";
echo "</ul>";

// 写入诊断日志
$log_content = "=== 诊断信息 " . date('Y-m-d H:i:s') . " ===\n";
$log_content .= "PHP版本: " . phpversion() . "\n";
$log_content .= "SAPI: " . php_sapi_name() . "\n";
$log_content .= "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "\n";
$log_content .= "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'N/A') . "\n";
$log_content .= "SCRIPT_FILENAME: " . ($_SERVER['SCRIPT_FILENAME'] ?? 'N/A') . "\n";
$log_content .= "cgi.fix_pathinfo: " . ini_get('cgi.fix_pathinfo') . "\n";
$log_content .= "---\n\n";

file_put_contents('diagnose.log', $log_content, FILE_APPEND | LOCK_EX);
echo "<p>诊断信息已写入 diagnose.log</p>";
?>
