<?php
echo "<h1>API测试页面</h1>";

// 测试不同的API调用
$tests = [
    [
        'name' => '获取商户信息',
        'url' => 'api_proxy.php?action=get_merchant_info&merchant_id=8034567958'
    ],
    [
        'name' => '获取商品列表',
        'url' => 'api_proxy.php?action=get_product_list&merchant_id=8034567958'
    ],
    [
        'name' => '获取商品信息',
        'url' => 'api_proxy.php?action=get_product_info&product_id=1'
    ]
];

foreach ($tests as $test) {
    echo "<h2>{$test['name']}</h2>";
    echo "<p>URL: <a href='{$test['url']}' target='_blank'>{$test['url']}</a></p>";
    
    // 使用cURL调用API
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:4562/' . $test['url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'PHP-Test-Script/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>cURL错误: $error</p>";
    } else {
        echo "<p>HTTP状态码: $httpCode</p>";
        echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow: auto;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
    }
    
    echo "<hr>";
}

// 显示日志文件内容
echo "<h2>API日志文件内容</h2>";
$logFile = 'api_debug.log';
if (file_exists($logFile)) {
    $logContent = file_get_contents($logFile);
    echo "<pre style='background: #f0f0f0; padding: 10px; max-height: 400px; overflow: auto;'>";
    echo htmlspecialchars($logContent);
    echo "</pre>";
} else {
    echo "<p>日志文件不存在</p>";
}
?>
