<?php
// 最简单的测试版本
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 记录访问
$logFile = __DIR__ . '/simple_debug.log';
$logMessage = date('Y-m-d H:i:s') . " - 访问shop_simple.php\n";
$logMessage .= "GET: " . json_encode($_GET) . "\n";
$logMessage .= "URI: " . $_SERVER['REQUEST_URI'] . "\n";
$logMessage .= "---\n";
file_put_contents($logFile, $logMessage, FILE_APPEND);

// 检查参数
$sj = $_GET['sj'] ?? '';
$sp = $_GET['sp'] ?? '';

if (empty($sj) && empty($sp)) {
    http_response_code(404);
    echo "404 - 请提供sj或sp参数";
    exit;
}

$page_type = !empty($sj) ? 'merchant' : 'product';
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简化测试页面</title>
</head>
<body>
    <h1>简化测试页面</h1>
    <p>页面类型: <?php echo $page_type; ?></p>
    <p>商户ID: <?php echo htmlspecialchars($sj); ?></p>
    <p>商品ID: <?php echo htmlspecialchars($sp); ?></p>
    <p>当前时间: <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <h2>测试API调用</h2>
    <button onclick="testAPI()">测试API</button>
    <div id="result"></div>
    
    <script>
    function testAPI() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '测试中...';
        
        const merchantId = '<?php echo htmlspecialchars($sj); ?>';
        if (!merchantId) {
            resultDiv.innerHTML = '没有商户ID';
            return;
        }
        
        fetch('api_proxy.php?action=get_merchant_info&merchant_id=' + merchantId)
            .then(response => response.text())
            .then(data => {
                resultDiv.innerHTML = '<pre>' + data + '</pre>';
            })
            .catch(error => {
                resultDiv.innerHTML = '错误: ' + error;
            });
    }
    </script>
</body>
</html>
