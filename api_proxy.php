<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 日志函数
function writeLog($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] API: $message" . PHP_EOL;

    // 写入日志文件
    $logFile = __DIR__ . '/api_debug.log';
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

    // 同时输出到错误日志
    error_log($logMessage);
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 记录API访问日志
writeLog("=== 新的API请求开始 ===");
writeLog("请求URI: " . $_SERVER['REQUEST_URI']);
writeLog("请求方法: " . $_SERVER['REQUEST_METHOD']);
writeLog("GET参数: " . json_encode($_GET));
writeLog("POST参数: " . json_encode($_POST));

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    writeLog("处理OPTIONS请求");
    exit(0);
}

// API基础URL - 外部API
define('API_BASE_URL', 'https://cloudshop.qnm6.top/');

// 商户秘钥生成算法（与bot.py保持一致）
function generate_merchant_secret($merchant_id) {
    $key = "yjsyjs_merchant_secret_key_2024";
    $salt = "yjsyjs_salt_2024";
    
    // 组合商户ID、密钥和盐值
    $combined = $merchant_id . $key . $salt;
    
    // 使用MD5生成32位字符串
    $hash_value = md5($combined);
    
    // 添加商户ID前缀，确保唯一性
    $prefix = substr($merchant_id, 0, 3);
    
    // 组合成最终的商户秘钥
    $merchant_secret = $prefix . "_" . $hash_value;
    
    return $merchant_secret;
}

// 调用上游API
function call_upstream_api($endpoint, $params = []) {
    writeLog("开始调用上游API: $endpoint");
    writeLog("API参数: " . json_encode($params));

    $url = API_BASE_URL . $endpoint;

    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }

    writeLog("完整API URL: $url");

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'header' => 'User-Agent: PHP-API-Proxy/1.0'
        ]
    ]);

    writeLog("开始发送HTTP请求");
    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        $error = error_get_last();
        writeLog("API调用失败: " . ($error['message'] ?? '未知错误'), 'ERROR');
        return [
            'status' => 'error',
            'message' => 'API调用失败: ' . ($error['message'] ?? '未知错误'),
            'data' => null
        ];
    }

    writeLog("API响应长度: " . strlen($response));
    writeLog("API原始响应: " . substr($response, 0, 500) . (strlen($response) > 500 ? '...' : ''));

    $decoded = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        writeLog("JSON解析失败: " . json_last_error_msg(), 'ERROR');
        return [
            'status' => 'error',
            'message' => 'API响应格式错误: ' . json_last_error_msg(),
            'data' => null
        ];
    }

    writeLog("API调用成功，返回状态: " . ($decoded['status'] ?? 'unknown'));
    return $decoded;
}

// 获取请求参数
$action = $_GET['action'] ?? '';
$merchant_id = $_GET['merchant_id'] ?? '';
$product_id = $_GET['product_id'] ?? '';
$customer_contact = $_GET['customer_contact'] ?? '';
$pay_type = $_GET['pay_type'] ?? 'wxpay';
$order_id = $_GET['order_id'] ?? '';

writeLog("解析请求参数:");
writeLog("- action: '$action'");
writeLog("- merchant_id: '$merchant_id'");
writeLog("- product_id: '$product_id'");
writeLog("- customer_contact: '$customer_contact'");
writeLog("- pay_type: '$pay_type'");
writeLog("- order_id: '$order_id'");

// 路由处理
writeLog("开始路由处理，action: $action");
switch ($action) {
    case 'get_merchant_info':
        writeLog("处理get_merchant_info请求");
        if (empty($merchant_id)) {
            writeLog("merchant_id参数为空", 'ERROR');
            $result = ['status' => 'error', 'message' => '缺少商户ID参数'];
        } else {
            $result = call_upstream_api('get_merchant_info.php', ['merchant_id' => $merchant_id]);
        }
        break;

    case 'get_product_list':
        writeLog("处理get_product_list请求");
        if (empty($merchant_id)) {
            writeLog("merchant_id参数为空", 'ERROR');
            $result = ['status' => 'error', 'message' => '缺少商户ID参数'];
        } else {
            $merchant_secret = generate_merchant_secret($merchant_id);
            writeLog("生成商户秘钥: $merchant_secret");
            $result = call_upstream_api('get_product_list.php', ['merchant_secret' => $merchant_secret]);
        }
        break;

    case 'get_product_info':
        writeLog("处理get_product_info请求");
        if (empty($product_id)) {
            writeLog("product_id参数为空", 'ERROR');
            $result = ['status' => 'error', 'message' => '缺少商品ID参数'];
        } else {
            $result = call_upstream_api('get_product_info.php', ['product_id' => $product_id]);
        }
        break;

    case 'create_order':
        writeLog("处理create_order请求");
        if (empty($customer_contact) || empty($product_id)) {
            writeLog("create_order缺少必要参数", 'ERROR');
            $result = ['status' => 'error', 'message' => '缺少必要参数'];
        } else {
            $result = call_upstream_api('create_order.php', [
                'customer_contact' => $customer_contact,
                'product_id' => $product_id,
                'pay_type' => $pay_type
            ]);
        }
        break;

    case 'check_payment_status':
        writeLog("处理check_payment_status请求");
        if (empty($order_id)) {
            writeLog("order_id参数为空", 'ERROR');
            $result = ['status' => 'error', 'message' => '缺少订单ID参数'];
        } else {
            $result = call_upstream_api('check_payment_status.php', ['order_id' => $order_id]);
        }
        break;

    default:
        writeLog("无效的action: $action", 'ERROR');
        $result = [
            'status' => 'error',
            'message' => '无效的操作: ' . $action,
            'data' => null
        ];
        break;
}

writeLog("最终返回结果: " . json_encode($result, JSON_UNESCAPED_UNICODE));
echo json_encode($result, JSON_UNESCAPED_UNICODE);
?>
