<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// API基础URL
define('API_BASE_URL', 'https://cloudshop.qnm6.top/');

// 商户秘钥生成算法（与bot.py保持一致）
function generate_merchant_secret($merchant_id) {
    $key = "yjsyjs_merchant_secret_key_2024";
    $salt = "yjsyjs_salt_2024";
    
    // 组合商户ID、密钥和盐值
    $combined = $merchant_id . $key . $salt;
    
    // 使用MD5生成32位字符串
    $hash_value = md5($combined);
    
    // 添加商户ID前缀，确保唯一性
    $prefix = substr($merchant_id, 0, 3);
    
    // 组合成最终的商户秘钥
    $merchant_secret = $prefix . "_" . $hash_value;
    
    return $merchant_secret;
}

// 调用上游API
function call_upstream_api($endpoint, $params = []) {
    $url = API_BASE_URL . $endpoint;
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'header' => 'User-Agent: PHP-API-Proxy/1.0'
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        return [
            'status' => 'error',
            'message' => 'API调用失败',
            'data' => null
        ];
    }
    
    $decoded = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return [
            'status' => 'error',
            'message' => 'API响应格式错误',
            'data' => null
        ];
    }
    
    return $decoded;
}

// 获取请求参数
$action = $_GET['action'] ?? '';
$merchant_id = $_GET['merchant_id'] ?? '';
$product_id = $_GET['product_id'] ?? '';
$customer_contact = $_GET['customer_contact'] ?? '';
$pay_type = $_GET['pay_type'] ?? 'wxpay';
$order_id = $_GET['order_id'] ?? '';

// 路由处理
switch ($action) {
    case 'get_merchant_info':
        if (empty($merchant_id)) {
            echo json_encode(['status' => 'error', 'message' => '缺少商户ID参数']);
            exit;
        }
        $result = call_upstream_api('get_merchant_info.php', ['merchant_id' => $merchant_id]);
        break;
        
    case 'get_product_list':
        if (empty($merchant_id)) {
            echo json_encode(['status' => 'error', 'message' => '缺少商户ID参数']);
            exit;
        }
        $merchant_secret = generate_merchant_secret($merchant_id);
        $result = call_upstream_api('get_product_list.php', ['merchant_secret' => $merchant_secret]);
        break;
        
    case 'get_product_info':
        if (empty($product_id)) {
            echo json_encode(['status' => 'error', 'message' => '缺少商品ID参数']);
            exit;
        }
        $result = call_upstream_api('get_product_info.php', ['product_id' => $product_id]);
        break;
        
    case 'create_order':
        if (empty($customer_contact) || empty($product_id)) {
            echo json_encode(['status' => 'error', 'message' => '缺少必要参数']);
            exit;
        }
        $result = call_upstream_api('create_order.php', [
            'customer_contact' => $customer_contact,
            'product_id' => $product_id,
            'pay_type' => $pay_type
        ]);
        break;
        
    case 'check_payment_status':
        if (empty($order_id)) {
            echo json_encode(['status' => 'error', 'message' => '缺少订单ID参数']);
            exit;
        }
        $result = call_upstream_api('check_payment_status.php', ['order_id' => $order_id]);
        break;
        
    default:
        $result = [
            'status' => 'error',
            'message' => '无效的操作',
            'data' => null
        ];
        break;
}

echo json_encode($result, JSON_UNESCAPED_UNICODE);
?>
