<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看器</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .log-section {
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-header {
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .log-content {
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            background: #f8f8f8;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-line {
            margin-bottom: 2px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-info { background: #e8f5e8; }
        .log-error { background: #ffe8e8; }
        .log-warning { background: #fff3cd; }
        .refresh-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .refresh-btn:hover {
            background: #005a87;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>日志查看器</h1>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="shop-requests">0</div>
                <div class="stat-label">Shop 请求</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="api-requests">0</div>
                <div class="stat-label">API 请求</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-errors">0</div>
                <div class="stat-label">错误数量</div>
            </div>
        </div>
        
        <div class="log-section">
            <div class="log-header">
                <h2>Shop 访问日志 (shop_debug.log)</h2>
                <button class="refresh-btn" onclick="refreshLogs()">刷新</button>
            </div>
            <div class="log-content" id="shop-log">
                <?php
                $shopLogFile = 'shop_debug.log';
                if (file_exists($shopLogFile)) {
                    $lines = file($shopLogFile);
                    $lines = array_slice($lines, -50); // 显示最后50行
                    foreach ($lines as $line) {
                        $line = htmlspecialchars(trim($line));
                        $class = 'log-info';
                        if (strpos($line, '[ERROR]') !== false) {
                            $class = 'log-error';
                        } elseif (strpos($line, '[WARNING]') !== false) {
                            $class = 'log-warning';
                        }
                        echo "<div class='log-line $class'>$line</div>";
                    }
                } else {
                    echo "<div class='log-line'>日志文件不存在</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="log-section">
            <div class="log-header">
                <h2>API 代理日志 (api_debug.log)</h2>
                <button class="refresh-btn" onclick="refreshLogs()">刷新</button>
            </div>
            <div class="log-content" id="api-log">
                <?php
                $apiLogFile = 'api_debug.log';
                if (file_exists($apiLogFile)) {
                    $lines = file($apiLogFile);
                    $lines = array_slice($lines, -50); // 显示最后50行
                    foreach ($lines as $line) {
                        $line = htmlspecialchars(trim($line));
                        $class = 'log-info';
                        if (strpos($line, '[ERROR]') !== false) {
                            $class = 'log-error';
                        } elseif (strpos($line, '[WARNING]') !== false) {
                            $class = 'log-warning';
                        }
                        echo "<div class='log-line $class'>$line</div>";
                    }
                } else {
                    echo "<div class='log-line'>日志文件不存在</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="log-section">
            <div class="log-header">
                <h2>测试链接</h2>
            </div>
            <div style="padding: 15px;">
                <p><a href="shop.php?sj=8034567958" target="_blank">测试商家页面</a></p>
                <p><a href="shop.php?sp=73" target="_blank">测试商品页面</a></p>
                <p><a href="api_proxy.php?action=get_merchant_info&merchant_id=8034567958" target="_blank">测试API - 获取商户信息</a></p>
                <p><a href="api_proxy.php?action=get_product_list&merchant_id=8034567958" target="_blank">测试API - 获取商品列表</a></p>
                <p><a href="api_proxy.php?action=get_product_info&product_id=73" target="_blank">测试API - 获取商品信息</a></p>
            </div>
        </div>
    </div>
    
    <script>
        function refreshLogs() {
            location.reload();
        }
        
        // 自动刷新
        setInterval(refreshLogs, 30000); // 每30秒刷新一次
        
        // 统计数据
        function updateStats() {
            const shopLog = document.getElementById('shop-log').textContent;
            const apiLog = document.getElementById('api-log').textContent;
            
            const shopRequests = (shopLog.match(/=== 新的访问开始 ===/g) || []).length;
            const apiRequests = (apiLog.match(/=== 新的API请求开始 ===/g) || []).length;
            const totalErrors = (shopLog.match(/\[ERROR\]/g) || []).length + (apiLog.match(/\[ERROR\]/g) || []).length;
            
            document.getElementById('shop-requests').textContent = shopRequests;
            document.getElementById('api-requests').textContent = apiRequests;
            document.getElementById('total-errors').textContent = totalErrors;
        }
        
        updateStats();
    </script>
</body>
</html>
