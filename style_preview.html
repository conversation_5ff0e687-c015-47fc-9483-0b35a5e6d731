<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式预览 - iOS风格极简设计</title>
    <style>
        /* 复制shop.php中的CSS变量和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        :root {
            /* 蓝色系专业感 */
            --primary-blue: #007AFF;
            --primary-blue-light: #5AC8FA;
            --primary-blue-dark: #0051D5;
            --blue-gradient: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
            
            /* 橙色系活力感 */
            --accent-orange: #FF9500;
            --accent-orange-light: #FFCC02;
            --orange-gradient: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
            
            /* iOS系统色彩 */
            --background-primary: #F2F2F7;
            --background-secondary: #FFFFFF;
            --background-tertiary: #F8F9FA;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --text-tertiary: #C7C7CC;
            --separator: #C6C6C8;
            
            /* 阴影 */
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
            --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.15);
            
            /* 圆角 */
            --radius-small: 8px;
            --radius-medium: 12px;
            --radius-large: 16px;
            --radius-xl: 20px;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: var(--background-primary);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 16px;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .preview-section {
            margin-bottom: 40px;
        }
        
        .preview-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
        }
        
        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 14px 28px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            margin: 8px;
            min-height: 50px;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: var(--blue-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
        }
        
        .btn-wxpay {
            background: linear-gradient(135deg, #09BB07 0%, #00D100 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(9, 187, 7, 0.3);
        }
        
        .btn-alipay {
            background: linear-gradient(135deg, #1677FF 0%, #40A9FF 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(22, 119, 255, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        /* 卡片样式 */
        .card {
            background: var(--background-secondary);
            border-radius: var(--radius-large);
            box-shadow: var(--shadow-medium);
            margin-bottom: 24px;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .card-header {
            background: var(--blue-gradient);
            color: white;
            padding: 32px 24px;
            text-align: center;
        }
        
        .card-body {
            padding: 24px;
        }
        
        /* 商品项样式 */
        .product-item {
            background: var(--background-secondary);
            border-radius: var(--radius-medium);
            padding: 20px;
            margin-bottom: 16px;
            border: 1px solid var(--separator);
            position: relative;
            overflow: hidden;
        }
        
        .product-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--orange-gradient);
        }
        
        .product-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
        }
        
        .product-price {
            font-size: 24px;
            color: var(--accent-orange);
            font-weight: 700;
            margin-bottom: 12px;
        }
        
        .product-stock {
            font-size: 14px;
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 20px;
            display: inline-block;
            background: rgba(52, 199, 89, 0.1);
            color: #34C759;
        }
        
        /* 颜色展示 */
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 30px;
        }
        
        .color-item {
            text-align: center;
            padding: 20px;
            border-radius: var(--radius-medium);
            color: white;
            font-weight: 500;
        }
        
        .color-blue { background: var(--primary-blue); }
        .color-blue-light { background: var(--primary-blue-light); }
        .color-orange { background: var(--accent-orange); }
        .color-orange-light { background: var(--accent-orange-light); }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 40px; font-size: 32px; font-weight: 700;">iOS风格极简设计预览</h1>
        
        <div class="preview-section">
            <h2 class="preview-title">🎨 色彩系统</h2>
            <div class="color-palette">
                <div class="color-item color-blue">主蓝色 #007AFF</div>
                <div class="color-item color-blue-light">浅蓝色 #5AC8FA</div>
                <div class="color-item color-orange">主橙色 #FF9500</div>
                <div class="color-item color-orange-light">浅橙色 #FFCC02</div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 class="preview-title">🔘 按钮系统</h2>
            <div>
                <button class="btn btn-primary">主要按钮</button>
                <button class="btn btn-wxpay">💳 微信支付</button>
                <button class="btn btn-alipay">💳 支付宝</button>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 class="preview-title">📱 卡片组件</h2>
            <div class="card">
                <div class="card-header">
                    <h1>🏪 商店标题</h1>
                </div>
                <div class="card-body">
                    <p>这是卡片内容区域，展示了iOS风格的设计元素。</p>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 class="preview-title">🛍️ 商品展示</h2>
            <div class="product-item">
                <div class="product-name">示例商品名称</div>
                <div class="product-price">¥99.00</div>
                <p style="color: var(--text-secondary); margin-bottom: 12px;">这是商品描述信息，展示了产品的主要特点和功能。</p>
                <div class="product-stock">✅ 库存: 10 件</div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 class="preview-title">📱 响应式测试</h2>
            <p style="color: var(--text-secondary);">
                本设计完全适配PC端和移动端，使用了现代CSS Grid和Flexbox布局，
                确保在各种屏幕尺寸下都能提供最佳的用户体验。
            </p>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="shop.php?sj=8034567958" class="btn btn-primary">查看实际页面</a>
        </div>
    </div>
</body>
</html>
