<?php
// 检查参数
$sj = $_GET['sj'] ?? '';
$sp = $_GET['sp'] ?? '';

// 如果没有传递sj或sp参数，返回404
if (empty($sj) && empty($sp)) {
    http_response_code(404);
    echo '<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>404 Not Found</h1><p>请提供sj或sp参数</p></body></html>';
    exit;
}

// 确定页面类型
$page_type = !empty($sj) ? 'merchant' : 'product';
$merchant_id = $sj;
$product_id = $sp;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_type === 'merchant' ? '商家店铺' : '商品详情'; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #e74c3c;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .product-item {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        
        .product-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-wxpay {
            background: linear-gradient(45deg, #1aad19, #2dc653);
        }
        
        .btn-alipay {
            background: linear-gradient(45deg, #1678ff, #40a9ff);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
            }
            
            .btn {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="app">
            <div class="loading">
                <p>加载中...</p>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'api_proxy.php';
        const pageType = '<?php echo $page_type; ?>';
        const merchantId = '<?php echo htmlspecialchars($merchant_id); ?>';
        const productId = '<?php echo htmlspecialchars($product_id); ?>';
        
        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function formatText(text) {
            if (!text) return '';
            return escapeHtml(text).replace(/\\n/g, '\n');
        }
        
        // API调用函数
        async function callAPI(action, params = {}) {
            try {
                const url = new URL(API_URL, window.location.origin);
                url.searchParams.append('action', action);
                
                Object.keys(params).forEach(key => {
                    if (params[key] !== null && params[key] !== undefined) {
                        url.searchParams.append(key, params[key]);
                    }
                });
                
                console.log('API调用:', url.toString());
                const response = await fetch(url);
                const data = await response.json();
                console.log('API响应:', data);
                return data;
            } catch (error) {
                console.error('API调用失败:', error);
                return {
                    status: 'error',
                    message: '网络请求失败: ' + error.message,
                    data: null
                };
            }
        }
        
        // 渲染商家页面
        async function renderMerchantPage() {
            const app = document.getElementById('app');
            
            try {
                // 获取商家信息
                const merchantInfo = await callAPI('get_merchant_info', { merchant_id: merchantId });
                
                if (merchantInfo.status !== 'success') {
                    app.innerHTML = `<div class="error">❌ ${merchantInfo.message}</div>`;
                    return;
                }
                
                // 获取商品列表
                const productList = await callAPI('get_product_list', { merchant_id: merchantId });
                
                const merchant = merchantInfo.data;
                const products = productList.status === 'success' ? productList.data : [];
                
                app.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h1>🏪 ${formatText(merchant.shop_name)}</h1>
                        </div>
                        <div class="card-body">
                            <div class="shop-description" style="white-space: pre-wrap; margin-bottom: 20px;">${formatText(merchant.shop_description)}</div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h2>📦 商品列表</h2>
                        </div>
                        <div class="card-body">
                            <div class="product-grid">
                                ${products.length === 0 ? 
                                    '<div class="error">⚠️ 该商户还没有商品</div>' : 
                                    products.map(product => `
                                        <div class="product-item" onclick="showProductDetail('${product.product_id}')">
                                            <div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">${formatText(product.product_name)}</div>
                                            <div style="font-size: 20px; color: #e74c3c; font-weight: bold; margin-bottom: 8px;">¥${product.product_price}</div>
                                            <div style="color: #666; font-size: 14px; margin-bottom: 10px; white-space: pre-wrap;">${formatText(product.product_description)}</div>
                                            <div style="font-size: 14px; color: ${parseInt(product.stock_quantity) <= 0 ? '#e74c3c' : '#27ae60'};">
                                                ${parseInt(product.stock_quantity) <= 0 ? '❌ 已售罄' : `✅ 库存: ${product.stock_quantity} 件`}
                                            </div>
                                        </div>
                                    `).join('')
                                }
                            </div>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                app.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
            }
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (pageType === 'merchant') {
                renderMerchantPage();
            } else if (pageType === 'product') {
                // TODO: 实现商品详情页面
                document.getElementById('app').innerHTML = '<div class="error">商品详情页面开发中...</div>';
            }
        });
        
        // 全局函数
        window.showProductDetail = function(productId) {
            alert('商品详情功能开发中，商品ID: ' + productId);
        };
    </script>
</body>
</html>
