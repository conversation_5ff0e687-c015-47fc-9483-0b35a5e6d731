<?php
echo "<h1>PHP配置检查</h1>";

echo "<h2>基本信息</h2>";
echo "PHP版本: " . phpversion() . "<br>";
echo "SAPI: " . php_sapi_name() . "<br>";
echo "当前目录: " . getcwd() . "<br>";
echo "脚本目录: " . __DIR__ . "<br>";

echo "<h2>重要配置</h2>";
echo "open_basedir: " . ini_get('open_basedir') . "<br>";
echo "cgi.fix_pathinfo: " . ini_get('cgi.fix_pathinfo') . "<br>";
echo "doc_root: " . ini_get('doc_root') . "<br>";
echo "user_dir: " . ini_get('user_dir') . "<br>";

echo "<h2>服务器变量</h2>";
echo "SCRIPT_FILENAME: " . ($_SERVER['SCRIPT_FILENAME'] ?? 'N/A') . "<br>";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'N/A') . "<br>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "<br>";
echo "DOCUMENT_ROOT: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'N/A') . "<br>";

echo "<h2>文件访问测试</h2>";
$test_files = ['shop.php', 'api_proxy.php', 'simple_test.php'];
foreach ($test_files as $file) {
    $full_path = __DIR__ . '/' . $file;
    if (file_exists($full_path)) {
        echo "✅ $file 存在且可访问<br>";
    } else {
        echo "❌ $file 不存在或不可访问<br>";
    }
}

echo "<h2>测试链接</h2>";
echo '<a href="simple_test.php?test=1">测试simple_test.php</a><br>';
echo '<a href="shop.php?sj=8034567958">测试shop.php</a><br>';
echo '<a href="api_proxy.php?action=get_merchant_info&merchant_id=8034567958">测试api_proxy.php</a><br>';
?>
