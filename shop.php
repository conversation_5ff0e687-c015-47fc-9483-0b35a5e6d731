<?php
// 检查参数
$sj = $_GET['sj'] ?? '';
$sp = $_GET['sp'] ?? '';

// 如果没有传递sj或sp参数，返回404
if (empty($sj) && empty($sp)) {
    http_response_code(404);
    echo '<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>404 Not Found</h1></body></html>';
    exit;
}

// 确定页面类型
$page_type = !empty($sj) ? 'merchant' : 'product';
$merchant_id = $sj;
$product_id = $sp;
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_type === 'merchant' ? '商家店铺' : '商品详情'; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .card-body {
            padding: 20px;
        }
        
        .shop-info h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .shop-description {
            font-size: 16px;
            line-height: 1.6;
            color: #666;
            margin-bottom: 20px;
            white-space: pre-wrap;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .product-item {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        
        .product-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .product-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .product-price {
            font-size: 20px;
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .product-description {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            white-space: pre-wrap;
        }
        
        .product-stock {
            font-size: 14px;
            color: #27ae60;
        }
        
        .product-stock.out-of-stock {
            color: #e74c3c;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .btn-wxpay {
            background: linear-gradient(45deg, #1aad19, #2dc653);
        }
        
        .btn-alipay {
            background: linear-gradient(45deg, #1678ff, #40a9ff);
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #e74c3c;
        }
        
        .order-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .order-info h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .order-detail {
            margin-bottom: 10px;
        }
        
        .order-detail strong {
            color: #333;
        }
        
        .payment-buttons {
            text-align: center;
            margin-top: 20px;
        }
        
        .delivery-content {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .back-btn {
            background: #6c757d;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .product-grid {
                grid-template-columns: 1fr;
            }
            
            .btn {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="app">
            <div class="loading">
                <p>加载中...</p>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'api_proxy.php';
        const pageType = '<?php echo $page_type; ?>';
        const merchantId = '<?php echo htmlspecialchars($merchant_id); ?>';
        const productId = '<?php echo htmlspecialchars($product_id); ?>';
        
        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        function formatText(text) {
            if (!text) return '';
            // 处理换行符和emoji
            return escapeHtml(text).replace(/\\n/g, '\n');
        }
        
        // API调用函数
        async function callAPI(action, params = {}) {
            try {
                const url = new URL(API_URL, window.location.origin);
                url.searchParams.append('action', action);
                
                Object.keys(params).forEach(key => {
                    if (params[key] !== null && params[key] !== undefined) {
                        url.searchParams.append(key, params[key]);
                    }
                });
                
                const response = await fetch(url);
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API调用失败:', error);
                return {
                    status: 'error',
                    message: '网络请求失败',
                    data: null
                };
            }
        }

        // 渲染商家页面
        async function renderMerchantPage() {
            const app = document.getElementById('app');

            try {
                // 获取商家信息
                const merchantInfo = await callAPI('get_merchant_info', { merchant_id: merchantId });

                if (merchantInfo.status !== 'success') {
                    app.innerHTML = `<div class="error">❌ ${merchantInfo.message}</div>`;
                    return;
                }

                // 获取商品列表
                const productList = await callAPI('get_product_list', { merchant_id: merchantId });

                const merchant = merchantInfo.data;
                const products = productList.status === 'success' ? productList.data : [];

                app.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h1>🏪 ${formatText(merchant.shop_name)}</h1>
                        </div>
                        <div class="card-body">
                            <div class="shop-info">
                                <div class="shop-description">${formatText(merchant.shop_description)}</div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h2>📦 商品列表</h2>
                        </div>
                        <div class="card-body">
                            <div class="product-grid" id="productGrid">
                                ${products.length === 0 ?
                                    '<div class="error">⚠️ 该商户还没有商品</div>' :
                                    products.map(product => `
                                        <div class="product-item" onclick="showProductDetail('${product.product_id}')">
                                            <div class="product-name">${formatText(product.product_name)}</div>
                                            <div class="product-price">¥${product.product_price}</div>
                                            <div class="product-description">${formatText(product.product_description)}</div>
                                            <div class="product-stock ${parseInt(product.stock_quantity) <= 0 ? 'out-of-stock' : ''}">
                                                ${parseInt(product.stock_quantity) <= 0 ? '❌ 已售罄' : `✅ 库存: ${product.stock_quantity} 件`}
                                            </div>
                                        </div>
                                    `).join('')
                                }
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                app.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
            }
        }

        // 渲染商品详情页面
        async function renderProductPage() {
            const app = document.getElementById('app');

            try {
                const productInfo = await callAPI('get_product_info', { product_id: productId });

                if (productInfo.status !== 'success') {
                    app.innerHTML = `<div class="error">❌ ${productInfo.message}</div>`;
                    return;
                }

                const product = productInfo.data;
                const stockQuantity = parseInt(product.stock_quantity);
                const isOutOfStock = stockQuantity <= 0;

                app.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h1>📦 商品详情</h1>
                        </div>
                        <div class="card-body">
                            <div class="product-name" style="font-size: 24px; margin-bottom: 15px;">${formatText(product.product_name)}</div>
                            <div class="product-price" style="font-size: 28px; margin-bottom: 15px;">¥${product.product_price}</div>
                            <div class="product-description" style="margin-bottom: 15px;">${formatText(product.product_description)}</div>
                            <div class="product-stock ${isOutOfStock ? 'out-of-stock' : ''}" style="font-size: 16px; margin-bottom: 20px;">
                                ${isOutOfStock ? '❌ 商品已售罄' : `✅ 库存: ${stockQuantity} 件`}
                            </div>

                            <div class="payment-buttons">
                                ${isOutOfStock ?
                                    '<button class="btn" disabled>❌ 商品已售罄</button>' :
                                    `
                                    <button class="btn btn-wxpay" onclick="createOrder('wxpay')">💳 微信支付</button>
                                    <button class="btn btn-alipay" onclick="createOrder('alipay')">💳 支付宝</button>
                                    `
                                }
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                app.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
            }
        }

        // 显示商品详情（从商家页面跳转）
        async function showProductDetail(productId) {
            const app = document.getElementById('app');
            app.innerHTML = '<div class="loading"><p>加载中...</p></div>';

            try {
                const productInfo = await callAPI('get_product_info', { product_id: productId });

                if (productInfo.status !== 'success') {
                    app.innerHTML = `<div class="error">❌ ${productInfo.message}</div>`;
                    return;
                }

                const product = productInfo.data;
                const stockQuantity = parseInt(product.stock_quantity);
                const isOutOfStock = stockQuantity <= 0;

                app.innerHTML = `
                    <button class="btn back-btn" onclick="renderMerchantPage()">🔙 返回商家页面</button>

                    <div class="card">
                        <div class="card-header">
                            <h1>📦 商品详情</h1>
                        </div>
                        <div class="card-body">
                            <div class="product-name" style="font-size: 24px; margin-bottom: 15px;">${formatText(product.product_name)}</div>
                            <div class="product-price" style="font-size: 28px; margin-bottom: 15px;">¥${product.product_price}</div>
                            <div class="product-description" style="margin-bottom: 15px;">${formatText(product.product_description)}</div>
                            <div class="product-stock ${isOutOfStock ? 'out-of-stock' : ''}" style="font-size: 16px; margin-bottom: 20px;">
                                ${isOutOfStock ? '❌ 商品已售罄' : `✅ 库存: ${stockQuantity} 件`}
                            </div>

                            <div class="payment-buttons">
                                ${isOutOfStock ?
                                    '<button class="btn" disabled>❌ 商品已售罄</button>' :
                                    `
                                    <button class="btn btn-wxpay" onclick="createOrder('wxpay', '${productId}')">💳 微信支付</button>
                                    <button class="btn btn-alipay" onclick="createOrder('alipay', '${productId}')">💳 支付宝</button>
                                    `
                                }
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                app.innerHTML = `<div class="error">❌ 加载失败: ${error.message}</div>`;
            }
        }

        // 创建订单
        async function createOrder(payType, pid = null) {
            const currentProductId = pid || productId;

            if (!currentProductId) {
                alert('商品ID不能为空');
                return;
            }

            // 生成客户联系方式（使用时间戳和随机数）
            const customerContact = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const app = document.getElementById('app');
            const originalContent = app.innerHTML;
            app.innerHTML = '<div class="loading"><p>创建订单中...</p></div>';

            try {
                const orderResult = await callAPI('create_order', {
                    customer_contact: customerContact,
                    product_id: currentProductId,
                    pay_type: payType
                });

                if (orderResult.status !== 'success') {
                    alert('❌ ' + orderResult.message);
                    app.innerHTML = originalContent;
                    return;
                }

                const orderInfo = orderResult.data.order_info;
                const paymentUrl = orderResult.data.payment_url;

                app.innerHTML = `
                    <div class="card">
                        <div class="card-header">
                            <h1>🛒 订单创建成功</h1>
                        </div>
                        <div class="card-body">
                            <div class="order-info">
                                <h3>订单信息</h3>
                                <div class="order-detail"><strong>订单号：</strong>${orderInfo.order_id}</div>
                                <div class="order-detail"><strong>商品名称：</strong>${formatText(orderInfo.product_name)}</div>
                                <div class="order-detail"><strong>需支付金额：</strong>¥${orderInfo.product_price}</div>
                                <div class="order-detail"><strong>购买人ID：</strong>${orderInfo.customer_contact}</div>
                                <div class="order-detail"><strong>订单状态：</strong>待支付</div>
                            </div>

                            <div class="payment-buttons">
                                <a href="${paymentUrl}" target="_blank" class="btn">💳 立即支付</a>
                                <button class="btn" onclick="checkPaymentStatus('${orderInfo.order_id}')">✅ 我已支付</button>
                            </div>
                        </div>
                    </div>
                `;

            } catch (error) {
                alert('❌ 创建订单失败: ' + error.message);
                app.innerHTML = originalContent;
            }
        }

        // 检查支付状态
        async function checkPaymentStatus(orderId) {
            const app = document.getElementById('app');
            const originalContent = app.innerHTML;
            app.innerHTML = '<div class="loading"><p>检查支付状态中...</p></div>';

            try {
                const paymentResult = await callAPI('check_payment_status', { order_id: orderId });

                if (paymentResult.status !== 'success') {
                    alert('❌ 检查支付状态失败');
                    app.innerHTML = originalContent;
                    return;
                }

                const orderData = paymentResult.data;
                const orderStatus = orderData.order_status;

                if (orderStatus === 'paid') {
                    // 支付成功
                    app.innerHTML = `
                        <div class="card">
                            <div class="card-header">
                                <h1>✅ 支付成功</h1>
                            </div>
                            <div class="card-body">
                                <div class="order-info">
                                    <h3>订单详情</h3>
                                    <div class="order-detail"><strong>订单号：</strong>${orderData.order_id}</div>
                                    <div class="order-detail"><strong>商品名称：</strong>${formatText(orderData.product_name)}</div>
                                    <div class="order-detail"><strong>商品金额：</strong>¥${orderData.product_price}</div>
                                    <div class="order-detail"><strong>购买人ID：</strong>${orderData.customer_contact}</div>
                                    <div class="order-detail"><strong>支付时间：</strong>${orderData.updated_at}</div>
                                </div>

                                <div class="delivery-content">
                                    <h3>🎁 发货内容：</h3>
                                    ${formatText(orderData.delivery_content)}
                                </div>

                                <div style="text-align: center; margin-top: 20px;">
                                    <a href="https://cloudshop.qnm6.top/tousu.html" target="_blank" class="btn">投诉此订单</a>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // 订单未支付
                    alert('❌ 订单未支付！请完成支付后再点击"我已支付"按钮');
                    app.innerHTML = originalContent;
                }

            } catch (error) {
                alert('❌ 检查支付状态失败: ' + error.message);
                app.innerHTML = originalContent;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (pageType === 'merchant') {
                renderMerchantPage();
            } else if (pageType === 'product') {
                renderProductPage();
            }
        });
    </script>
</body>
</html>
