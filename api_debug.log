[2025-08-04 12:55:43] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:55:43] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 12:55:43] [INFO] API: 请求方法: GET
[2025-08-04 12:55:43] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 12:55:43] [INFO] API: POST参数: []
[2025-08-04 12:55:43] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:55:43] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:55:43] [INFO] API: 解析请求参数:
[2025-08-04 12:55:43] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 12:55:43] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:55:43] [INFO] API: - product_id: ''
[2025-08-04 12:55:43] [INFO] API: - customer_contact: ''
[2025-08-04 12:55:43] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:55:43] [INFO] API: - order_id: ''
[2025-08-04 12:55:43] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 12:55:43] [INFO] API: 处理get_merchant_info请求
[2025-08-04 12:55:43] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 12:55:43] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 12:55:43] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 12:55:43] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:55:44] [INFO] API: API响应成功 (耗时: 938.36ms)，响应长度: 434
[2025-08-04 12:55:44] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:55:44] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:55:44] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 12:55:44] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:55:44] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:55:44] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:55:55] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:55:55] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 12:55:55] [INFO] API: 请求方法: GET
[2025-08-04 12:55:55] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 12:55:55] [INFO] API: POST参数: []
[2025-08-04 12:55:55] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:55:55] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:55:55] [INFO] API: 解析请求参数:
[2025-08-04 12:55:55] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 12:55:55] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:55:55] [INFO] API: - product_id: ''
[2025-08-04 12:55:55] [INFO] API: - customer_contact: ''
[2025-08-04 12:55:55] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:55:55] [INFO] API: - order_id: ''
[2025-08-04 12:55:55] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 12:55:55] [INFO] API: 处理get_merchant_info请求
[2025-08-04 12:55:55] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 12:55:55] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 12:55:55] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 12:55:55] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:55:55] [INFO] API: API响应成功 (耗时: 370.86ms)，响应长度: 434
[2025-08-04 12:55:55] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:55:55] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:55:55] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 12:55:55] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:55:55] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:55:55] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:55:55] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:55:55] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 12:55:55] [INFO] API: 请求方法: GET
[2025-08-04 12:55:55] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 12:55:55] [INFO] API: POST参数: []
[2025-08-04 12:55:55] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:55:55] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:55:55] [INFO] API: 解析请求参数:
[2025-08-04 12:55:55] [INFO] API: - action: 'get_product_list'
[2025-08-04 12:55:55] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:55:55] [INFO] API: - product_id: ''
[2025-08-04 12:55:55] [INFO] API: - customer_contact: ''
[2025-08-04 12:55:55] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:55:55] [INFO] API: - order_id: ''
[2025-08-04 12:55:55] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 12:55:55] [INFO] API: 处理get_product_list请求
[2025-08-04 12:55:55] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:55:55] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 12:55:55] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 12:55:55] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:55:55] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:55:55] [INFO] API: API响应成功 (耗时: 344.62ms)，响应长度: 3806
[2025-08-04 12:55:55] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 12:55:55] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:55:55] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 12:55:55] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:55:55] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 12:55:55] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:56:32] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:56:32] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 12:56:32] [INFO] API: 请求方法: GET
[2025-08-04 12:56:32] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 12:56:32] [INFO] API: POST参数: []
[2025-08-04 12:56:32] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:56:32] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:56:32] [INFO] API: 解析请求参数:
[2025-08-04 12:56:32] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 12:56:32] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:56:32] [INFO] API: - product_id: ''
[2025-08-04 12:56:32] [INFO] API: - customer_contact: ''
[2025-08-04 12:56:32] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:56:32] [INFO] API: - order_id: ''
[2025-08-04 12:56:32] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 12:56:32] [INFO] API: 处理get_merchant_info请求
[2025-08-04 12:56:32] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 12:56:32] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 12:56:32] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 12:56:32] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:56:32] [INFO] API: API响应成功 (耗时: 353.24ms)，响应长度: 434
[2025-08-04 12:56:32] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:56:32] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:56:32] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 12:56:32] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:56:32] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:56:32] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:56:32] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:56:32] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 12:56:32] [INFO] API: 请求方法: GET
[2025-08-04 12:56:32] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 12:56:32] [INFO] API: POST参数: []
[2025-08-04 12:56:32] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:56:32] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:56:32] [INFO] API: 解析请求参数:
[2025-08-04 12:56:32] [INFO] API: - action: 'get_product_list'
[2025-08-04 12:56:32] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:56:32] [INFO] API: - product_id: ''
[2025-08-04 12:56:32] [INFO] API: - customer_contact: ''
[2025-08-04 12:56:32] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:56:32] [INFO] API: - order_id: ''
[2025-08-04 12:56:32] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 12:56:32] [INFO] API: 处理get_product_list请求
[2025-08-04 12:56:32] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:56:32] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 12:56:32] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 12:56:32] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:56:32] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:56:33] [INFO] API: API响应成功 (耗时: 328.57ms)，响应长度: 3806
[2025-08-04 12:56:33] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 12:56:33] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:56:33] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 12:56:33] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:56:33] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 12:56:33] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:56:52] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:56:52] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 12:56:52] [INFO] API: 请求方法: GET
[2025-08-04 12:56:52] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 12:56:52] [INFO] API: POST参数: []
[2025-08-04 12:56:52] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:56:52] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:56:52] [INFO] API: 解析请求参数:
[2025-08-04 12:56:52] [INFO] API: - action: 'get_product_list'
[2025-08-04 12:56:52] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:56:52] [INFO] API: - product_id: ''
[2025-08-04 12:56:52] [INFO] API: - customer_contact: ''
[2025-08-04 12:56:52] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:56:52] [INFO] API: - order_id: ''
[2025-08-04 12:56:52] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 12:56:52] [INFO] API: 处理get_product_list请求
[2025-08-04 12:56:52] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:56:52] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 12:56:52] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 12:56:52] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:56:52] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:56:53] [INFO] API: API响应成功 (耗时: 372.65ms)，响应长度: 3806
[2025-08-04 12:56:53] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 12:56:53] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:56:53] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 12:56:53] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:56:53] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 12:56:53] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:57:13] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:57:13] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958&_t=1234567890
[2025-08-04 12:57:13] [INFO] API: 请求方法: GET
[2025-08-04 12:57:13] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958","_t":"1234567890"}
[2025-08-04 12:57:13] [INFO] API: POST参数: []
[2025-08-04 12:57:13] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:57:13] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:57:13] [INFO] API: 解析请求参数:
[2025-08-04 12:57:13] [INFO] API: - action: 'get_product_list'
[2025-08-04 12:57:13] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:57:13] [INFO] API: - product_id: ''
[2025-08-04 12:57:13] [INFO] API: - customer_contact: ''
[2025-08-04 12:57:13] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:57:13] [INFO] API: - order_id: ''
[2025-08-04 12:57:13] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 12:57:13] [INFO] API: 处理get_product_list请求
[2025-08-04 12:57:13] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:57:13] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 12:57:13] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 12:57:13] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:57:13] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:57:13] [INFO] API: API响应成功 (耗时: 332.74ms)，响应长度: 3806
[2025-08-04 12:57:13] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 12:57:13] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:57:13] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 12:57:13] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:57:13] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 12:57:13] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:57:32] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:57:32] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958&_t=9999999999
[2025-08-04 12:57:32] [INFO] API: 请求方法: GET
[2025-08-04 12:57:32] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958","_t":"9999999999"}
[2025-08-04 12:57:32] [INFO] API: POST参数: []
[2025-08-04 12:57:32] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:57:32] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:57:32] [INFO] API: 解析请求参数:
[2025-08-04 12:57:32] [INFO] API: - action: 'get_product_list'
[2025-08-04 12:57:32] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:57:32] [INFO] API: - product_id: ''
[2025-08-04 12:57:32] [INFO] API: - customer_contact: ''
[2025-08-04 12:57:32] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:57:32] [INFO] API: - order_id: ''
[2025-08-04 12:57:32] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 12:57:32] [INFO] API: 处理get_product_list请求
[2025-08-04 12:57:32] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:57:32] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 12:57:32] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 12:57:32] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:57:32] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:57:33] [INFO] API: API响应成功 (耗时: 371.86ms)，响应长度: 3806
[2025-08-04 12:57:33] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 12:57:33] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:57:33] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 12:57:33] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:57:33] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 12:57:33] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:58:11] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:58:11] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 12:58:11] [INFO] API: 请求方法: GET
[2025-08-04 12:58:11] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 12:58:11] [INFO] API: POST参数: []
[2025-08-04 12:58:11] [INFO] API: User-Agent: PHP-Test-Script/1.0
[2025-08-04 12:58:11] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:58:11] [INFO] API: 解析请求参数:
[2025-08-04 12:58:11] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 12:58:11] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:58:11] [INFO] API: - product_id: ''
[2025-08-04 12:58:11] [INFO] API: - customer_contact: ''
[2025-08-04 12:58:11] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:58:11] [INFO] API: - order_id: ''
[2025-08-04 12:58:11] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 12:58:11] [INFO] API: 处理get_merchant_info请求
[2025-08-04 12:58:11] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 12:58:11] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 12:58:11] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 12:58:11] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:58:11] [INFO] API: API响应成功 (耗时: 387.15ms)，响应长度: 434
[2025-08-04 12:58:11] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:58:11] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:58:11] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 12:58:11] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:58:11] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:58:11] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:58:11] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:58:11] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 12:58:11] [INFO] API: 请求方法: GET
[2025-08-04 12:58:11] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 12:58:11] [INFO] API: POST参数: []
[2025-08-04 12:58:11] [INFO] API: User-Agent: PHP-Test-Script/1.0
[2025-08-04 12:58:11] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:58:11] [INFO] API: 解析请求参数:
[2025-08-04 12:58:11] [INFO] API: - action: 'get_product_list'
[2025-08-04 12:58:11] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:58:11] [INFO] API: - product_id: ''
[2025-08-04 12:58:11] [INFO] API: - customer_contact: ''
[2025-08-04 12:58:11] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:58:11] [INFO] API: - order_id: ''
[2025-08-04 12:58:11] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 12:58:11] [INFO] API: 处理get_product_list请求
[2025-08-04 12:58:11] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:58:11] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 12:58:11] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 12:58:11] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:58:11] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:58:12] [INFO] API: API响应成功 (耗时: 346.93ms)，响应长度: 3806
[2025-08-04 12:58:12] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 12:58:12] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:58:12] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 12:58:12] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:58:12] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 12:58:12] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:58:12] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:58:12] [INFO] API: 请求URI: /api_proxy.php?action=get_product_info&product_id=1
[2025-08-04 12:58:12] [INFO] API: 请求方法: GET
[2025-08-04 12:58:12] [INFO] API: GET参数: {"action":"get_product_info","product_id":"1"}
[2025-08-04 12:58:12] [INFO] API: POST参数: []
[2025-08-04 12:58:12] [INFO] API: User-Agent: PHP-Test-Script/1.0
[2025-08-04 12:58:12] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:58:12] [INFO] API: 解析请求参数:
[2025-08-04 12:58:12] [INFO] API: - action: 'get_product_info'
[2025-08-04 12:58:12] [INFO] API: - merchant_id: ''
[2025-08-04 12:58:12] [INFO] API: - product_id: '1'
[2025-08-04 12:58:12] [INFO] API: - customer_contact: ''
[2025-08-04 12:58:12] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:58:12] [INFO] API: - order_id: ''
[2025-08-04 12:58:12] [INFO] API: 开始路由处理，action: get_product_info
[2025-08-04 12:58:12] [INFO] API: 处理get_product_info请求
[2025-08-04 12:58:12] [INFO] API: 开始调用上游API: get_product_info.php
[2025-08-04 12:58:12] [INFO] API: API参数: {"product_id":"1"}
[2025-08-04 12:58:12] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_info.php?product_id=1
[2025-08-04 12:58:12] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:58:12] [INFO] API: API响应成功 (耗时: 344.54ms)，响应长度: 58
[2025-08-04 12:58:12] [INFO] API: API原始响应前500字符: {"status":"error","message":"商品不存在","data":null}
[2025-08-04 12:58:12] [INFO] API: API调用成功，返回状态: error
[2025-08-04 12:58:12] [INFO] API: API返回消息: 商品不存在
[2025-08-04 12:58:12] [INFO] API: 最终返回结果状态: error
[2025-08-04 12:58:12] [INFO] API: 最终返回结果: {"status":"error","message":"商品不存在","data":null}
[2025-08-04 12:58:12] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:59:50] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:59:50] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 12:59:50] [INFO] API: 请求方法: GET
[2025-08-04 12:59:50] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 12:59:50] [INFO] API: POST参数: []
[2025-08-04 12:59:50] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:59:50] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:59:50] [INFO] API: 解析请求参数:
[2025-08-04 12:59:50] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 12:59:50] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:59:50] [INFO] API: - product_id: ''
[2025-08-04 12:59:50] [INFO] API: - customer_contact: ''
[2025-08-04 12:59:50] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:59:50] [INFO] API: - order_id: ''
[2025-08-04 12:59:50] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 12:59:50] [INFO] API: 处理get_merchant_info请求
[2025-08-04 12:59:50] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 12:59:50] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 12:59:50] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 12:59:50] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:59:51] [INFO] API: API响应成功 (耗时: 362.5ms)，响应长度: 434
[2025-08-04 12:59:51] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:59:51] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:59:51] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 12:59:51] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:59:51] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 12:59:51] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:59:51] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:59:51] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 12:59:51] [INFO] API: 请求方法: GET
[2025-08-04 12:59:51] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 12:59:51] [INFO] API: POST参数: []
[2025-08-04 12:59:51] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:59:51] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:59:51] [INFO] API: 解析请求参数:
[2025-08-04 12:59:51] [INFO] API: - action: 'get_product_list'
[2025-08-04 12:59:51] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 12:59:51] [INFO] API: - product_id: ''
[2025-08-04 12:59:51] [INFO] API: - customer_contact: ''
[2025-08-04 12:59:51] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:59:51] [INFO] API: - order_id: ''
[2025-08-04 12:59:51] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 12:59:51] [INFO] API: 处理get_product_list请求
[2025-08-04 12:59:51] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:59:51] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 12:59:51] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 12:59:51] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 12:59:51] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:59:51] [INFO] API: API响应成功 (耗时: 359.22ms)，响应长度: 3806
[2025-08-04 12:59:51] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 12:59:51] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:59:51] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 12:59:51] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:59:51] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 12:59:51] [INFO] API: === API请求处理完成 ===
[2025-08-04 12:59:56] [INFO] API: === 新的API请求开始 ===
[2025-08-04 12:59:56] [INFO] API: 请求URI: /api_proxy.php?action=get_product_info&product_id=73
[2025-08-04 12:59:56] [INFO] API: 请求方法: GET
[2025-08-04 12:59:56] [INFO] API: GET参数: {"action":"get_product_info","product_id":"73"}
[2025-08-04 12:59:56] [INFO] API: POST参数: []
[2025-08-04 12:59:56] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:59:56] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 12:59:56] [INFO] API: 解析请求参数:
[2025-08-04 12:59:56] [INFO] API: - action: 'get_product_info'
[2025-08-04 12:59:56] [INFO] API: - merchant_id: ''
[2025-08-04 12:59:56] [INFO] API: - product_id: '73'
[2025-08-04 12:59:56] [INFO] API: - customer_contact: ''
[2025-08-04 12:59:56] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 12:59:56] [INFO] API: - order_id: ''
[2025-08-04 12:59:56] [INFO] API: 开始路由处理，action: get_product_info
[2025-08-04 12:59:56] [INFO] API: 处理get_product_info请求
[2025-08-04 12:59:56] [INFO] API: 开始调用上游API: get_product_info.php
[2025-08-04 12:59:56] [INFO] API: API参数: {"product_id":"73"}
[2025-08-04 12:59:56] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_info.php?product_id=73
[2025-08-04 12:59:56] [INFO] API: 开始发送HTTP请求
[2025-08-04 12:59:57] [INFO] API: API响应成功 (耗时: 355.88ms)，响应长度: 344
[2025-08-04 12:59:57] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品信息成功","data":{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"}} 
[2025-08-04 12:59:57] [INFO] API: API调用成功，返回状态: success
[2025-08-04 12:59:57] [INFO] API: API返回消息: 获取商品信息成功
[2025-08-04 12:59:57] [INFO] API: 最终返回结果状态: success
[2025-08-04 12:59:57] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品信息成功","data":{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"}}
[2025-08-04 12:59:57] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:00:01] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:00:01] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 13:00:01] [INFO] API: 请求方法: GET
[2025-08-04 13:00:01] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 13:00:01] [INFO] API: POST参数: []
[2025-08-04 13:00:01] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:01] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:00:01] [INFO] API: 解析请求参数:
[2025-08-04 13:00:01] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 13:00:01] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 13:00:01] [INFO] API: - product_id: ''
[2025-08-04 13:00:01] [INFO] API: - customer_contact: ''
[2025-08-04 13:00:01] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:00:01] [INFO] API: - order_id: ''
[2025-08-04 13:00:01] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 13:00:01] [INFO] API: 处理get_merchant_info请求
[2025-08-04 13:00:01] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 13:00:01] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 13:00:01] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 13:00:01] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:00:02] [INFO] API: API响应成功 (耗时: 376.45ms)，响应长度: 434
[2025-08-04 13:00:02] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 13:00:02] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:00:02] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 13:00:02] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:00:02] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 13:00:02] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:00:02] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:00:02] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 13:00:02] [INFO] API: 请求方法: GET
[2025-08-04 13:00:02] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 13:00:02] [INFO] API: POST参数: []
[2025-08-04 13:00:02] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:02] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:00:02] [INFO] API: 解析请求参数:
[2025-08-04 13:00:02] [INFO] API: - action: 'get_product_list'
[2025-08-04 13:00:02] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 13:00:02] [INFO] API: - product_id: ''
[2025-08-04 13:00:02] [INFO] API: - customer_contact: ''
[2025-08-04 13:00:02] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:00:02] [INFO] API: - order_id: ''
[2025-08-04 13:00:02] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 13:00:02] [INFO] API: 处理get_product_list请求
[2025-08-04 13:00:02] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 13:00:02] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 13:00:02] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 13:00:02] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 13:00:02] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:00:02] [INFO] API: API响应成功 (耗时: 355.02ms)，响应长度: 3806
[2025-08-04 13:00:02] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 13:00:02] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:00:02] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 13:00:02] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:00:02] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 13:00:02] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:00:05] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:00:05] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 13:00:05] [INFO] API: 请求方法: GET
[2025-08-04 13:00:05] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 13:00:05] [INFO] API: POST参数: []
[2025-08-04 13:00:05] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:05] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:00:05] [INFO] API: 解析请求参数:
[2025-08-04 13:00:05] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 13:00:05] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 13:00:05] [INFO] API: - product_id: ''
[2025-08-04 13:00:05] [INFO] API: - customer_contact: ''
[2025-08-04 13:00:05] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:00:05] [INFO] API: - order_id: ''
[2025-08-04 13:00:05] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 13:00:05] [INFO] API: 处理get_merchant_info请求
[2025-08-04 13:00:05] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 13:00:05] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 13:00:05] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 13:00:05] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:00:05] [INFO] API: API响应成功 (耗时: 352.54ms)，响应长度: 434
[2025-08-04 13:00:05] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 13:00:05] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:00:05] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 13:00:05] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:00:05] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 13:00:05] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:00:05] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:00:05] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 13:00:05] [INFO] API: 请求方法: GET
[2025-08-04 13:00:05] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 13:00:05] [INFO] API: POST参数: []
[2025-08-04 13:00:05] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:05] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:00:05] [INFO] API: 解析请求参数:
[2025-08-04 13:00:05] [INFO] API: - action: 'get_product_list'
[2025-08-04 13:00:05] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 13:00:05] [INFO] API: - product_id: ''
[2025-08-04 13:00:05] [INFO] API: - customer_contact: ''
[2025-08-04 13:00:05] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:00:05] [INFO] API: - order_id: ''
[2025-08-04 13:00:05] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 13:00:05] [INFO] API: 处理get_product_list请求
[2025-08-04 13:00:05] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 13:00:05] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 13:00:05] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 13:00:05] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 13:00:05] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:00:06] [INFO] API: API响应成功 (耗时: 363.55ms)，响应长度: 3806
[2025-08-04 13:00:06] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 13:00:06] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:00:06] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 13:00:06] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:00:06] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 13:00:06] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:00:26] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:00:26] [INFO] API: 请求URI: /api_proxy.php?action=get_product_info&product_id=73
[2025-08-04 13:00:26] [INFO] API: 请求方法: GET
[2025-08-04 13:00:26] [INFO] API: GET参数: {"action":"get_product_info","product_id":"73"}
[2025-08-04 13:00:26] [INFO] API: POST参数: []
[2025-08-04 13:00:26] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:26] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:00:26] [INFO] API: 解析请求参数:
[2025-08-04 13:00:26] [INFO] API: - action: 'get_product_info'
[2025-08-04 13:00:26] [INFO] API: - merchant_id: ''
[2025-08-04 13:00:26] [INFO] API: - product_id: '73'
[2025-08-04 13:00:26] [INFO] API: - customer_contact: ''
[2025-08-04 13:00:26] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:00:26] [INFO] API: - order_id: ''
[2025-08-04 13:00:26] [INFO] API: 开始路由处理，action: get_product_info
[2025-08-04 13:00:26] [INFO] API: 处理get_product_info请求
[2025-08-04 13:00:26] [INFO] API: 开始调用上游API: get_product_info.php
[2025-08-04 13:00:26] [INFO] API: API参数: {"product_id":"73"}
[2025-08-04 13:00:26] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_info.php?product_id=73
[2025-08-04 13:00:26] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:00:26] [INFO] API: API响应成功 (耗时: 343.52ms)，响应长度: 344
[2025-08-04 13:00:26] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品信息成功","data":{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"}} 
[2025-08-04 13:00:26] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:00:26] [INFO] API: API返回消息: 获取商品信息成功
[2025-08-04 13:00:26] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:00:26] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品信息成功","data":{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"}}
[2025-08-04 13:00:26] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:00:37] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:00:37] [INFO] API: 请求URI: /api_proxy.php?action=get_product_info&product_id=8034567958
[2025-08-04 13:00:37] [INFO] API: 请求方法: GET
[2025-08-04 13:00:37] [INFO] API: GET参数: {"action":"get_product_info","product_id":"8034567958"}
[2025-08-04 13:00:37] [INFO] API: POST参数: []
[2025-08-04 13:00:37] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:37] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:00:37] [INFO] API: 解析请求参数:
[2025-08-04 13:00:37] [INFO] API: - action: 'get_product_info'
[2025-08-04 13:00:37] [INFO] API: - merchant_id: ''
[2025-08-04 13:00:37] [INFO] API: - product_id: '8034567958'
[2025-08-04 13:00:37] [INFO] API: - customer_contact: ''
[2025-08-04 13:00:37] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:00:37] [INFO] API: - order_id: ''
[2025-08-04 13:00:37] [INFO] API: 开始路由处理，action: get_product_info
[2025-08-04 13:00:37] [INFO] API: 处理get_product_info请求
[2025-08-04 13:00:37] [INFO] API: 开始调用上游API: get_product_info.php
[2025-08-04 13:00:37] [INFO] API: API参数: {"product_id":"8034567958"}
[2025-08-04 13:00:37] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_info.php?product_id=8034567958
[2025-08-04 13:00:37] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:00:37] [INFO] API: API响应成功 (耗时: 337.5ms)，响应长度: 58
[2025-08-04 13:00:37] [INFO] API: API原始响应前500字符: {"status":"error","message":"商品不存在","data":null}
[2025-08-04 13:00:37] [INFO] API: API调用成功，返回状态: error
[2025-08-04 13:00:37] [INFO] API: API返回消息: 商品不存在
[2025-08-04 13:00:37] [INFO] API: 最终返回结果状态: error
[2025-08-04 13:00:37] [INFO] API: 最终返回结果: {"status":"error","message":"商品不存在","data":null}
[2025-08-04 13:00:37] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:00:42] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:00:42] [INFO] API: 请求URI: /api_proxy.php?action=get_product_info&product_id=73
[2025-08-04 13:00:42] [INFO] API: 请求方法: GET
[2025-08-04 13:00:42] [INFO] API: GET参数: {"action":"get_product_info","product_id":"73"}
[2025-08-04 13:00:42] [INFO] API: POST参数: []
[2025-08-04 13:00:42] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:42] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:00:42] [INFO] API: 解析请求参数:
[2025-08-04 13:00:42] [INFO] API: - action: 'get_product_info'
[2025-08-04 13:00:42] [INFO] API: - merchant_id: ''
[2025-08-04 13:00:42] [INFO] API: - product_id: '73'
[2025-08-04 13:00:42] [INFO] API: - customer_contact: ''
[2025-08-04 13:00:42] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:00:42] [INFO] API: - order_id: ''
[2025-08-04 13:00:42] [INFO] API: 开始路由处理，action: get_product_info
[2025-08-04 13:00:42] [INFO] API: 处理get_product_info请求
[2025-08-04 13:00:42] [INFO] API: 开始调用上游API: get_product_info.php
[2025-08-04 13:00:42] [INFO] API: API参数: {"product_id":"73"}
[2025-08-04 13:00:42] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_info.php?product_id=73
[2025-08-04 13:00:42] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:00:42] [INFO] API: API响应成功 (耗时: 350.61ms)，响应长度: 344
[2025-08-04 13:00:42] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品信息成功","data":{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"}} 
[2025-08-04 13:00:42] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:00:42] [INFO] API: API返回消息: 获取商品信息成功
[2025-08-04 13:00:42] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:00:42] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品信息成功","data":{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"}}
[2025-08-04 13:00:42] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:01:15] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:01:15] [INFO] API: 请求URI: /api_proxy.php?action=get_merchant_info&merchant_id=8034567958
[2025-08-04 13:01:15] [INFO] API: 请求方法: GET
[2025-08-04 13:01:15] [INFO] API: GET参数: {"action":"get_merchant_info","merchant_id":"8034567958"}
[2025-08-04 13:01:15] [INFO] API: POST参数: []
[2025-08-04 13:01:15] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:01:15] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:01:15] [INFO] API: 解析请求参数:
[2025-08-04 13:01:15] [INFO] API: - action: 'get_merchant_info'
[2025-08-04 13:01:15] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 13:01:15] [INFO] API: - product_id: ''
[2025-08-04 13:01:15] [INFO] API: - customer_contact: ''
[2025-08-04 13:01:15] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:01:15] [INFO] API: - order_id: ''
[2025-08-04 13:01:15] [INFO] API: 开始路由处理，action: get_merchant_info
[2025-08-04 13:01:15] [INFO] API: 处理get_merchant_info请求
[2025-08-04 13:01:15] [INFO] API: 开始调用上游API: get_merchant_info.php
[2025-08-04 13:01:15] [INFO] API: API参数: {"merchant_id":"8034567958"}
[2025-08-04 13:01:15] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958
[2025-08-04 13:01:15] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:01:15] [INFO] API: API响应成功 (耗时: 336.94ms)，响应长度: 434
[2025-08-04 13:01:15] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 13:01:15] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:01:15] [INFO] API: API返回消息: 获取商户信息成功
[2025-08-04 13:01:15] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:01:15] [INFO] API: 最终返回结果: {"status":"success","message":"获取商户信息成功","data":{"shop_name":"iDatas在线商城","shop_description":"Telegram官方客服: @Riverkefu_bot\n(其他任何地方称其为店铺客服均为冒充!!)\n\n售前咨询 售后服务 请联系上述Tg","merchant_balance":"1463.86","payment_qr_code":"admin\/qrcodes\/merchant_8034567958_qr_1751109294.png","created_at":"2025-06-28 17:38:32","updated_at":"2025-08-04 12:55:26"}}
[2025-08-04 13:01:15] [INFO] API: === API请求处理完成 ===
[2025-08-04 13:01:16] [INFO] API: === 新的API请求开始 ===
[2025-08-04 13:01:16] [INFO] API: 请求URI: /api_proxy.php?action=get_product_list&merchant_id=8034567958
[2025-08-04 13:01:16] [INFO] API: 请求方法: GET
[2025-08-04 13:01:16] [INFO] API: GET参数: {"action":"get_product_list","merchant_id":"8034567958"}
[2025-08-04 13:01:16] [INFO] API: POST参数: []
[2025-08-04 13:01:16] [INFO] API: User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:01:16] [INFO] API: 客户端IP: 127.0.0.1
[2025-08-04 13:01:16] [INFO] API: 解析请求参数:
[2025-08-04 13:01:16] [INFO] API: - action: 'get_product_list'
[2025-08-04 13:01:16] [INFO] API: - merchant_id: '8034567958'
[2025-08-04 13:01:16] [INFO] API: - product_id: ''
[2025-08-04 13:01:16] [INFO] API: - customer_contact: ''
[2025-08-04 13:01:16] [INFO] API: - pay_type: 'wxpay'
[2025-08-04 13:01:16] [INFO] API: - order_id: ''
[2025-08-04 13:01:16] [INFO] API: 开始路由处理，action: get_product_list
[2025-08-04 13:01:16] [INFO] API: 处理get_product_list请求
[2025-08-04 13:01:16] [INFO] API: 生成商户秘钥: 803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 13:01:16] [INFO] API: 开始调用上游API: get_product_list.php
[2025-08-04 13:01:16] [INFO] API: API参数: {"merchant_secret":"803_d8685f654c84c0cefc10c56b2b93b47b"}
[2025-08-04 13:01:16] [INFO] API: 完整API URL: https://cloudshop.qnm6.top/get_product_list.php?merchant_secret=803_d8685f654c84c0cefc10c56b2b93b47b
[2025-08-04 13:01:16] [INFO] API: 开始发送HTTP请求
[2025-08-04 13:01:16] [INFO] API: API响应成功 (耗时: 326.96ms)，响应长度: 3806
[2025-08-04 13:01:16] [INFO] API: API原始响应前500字符: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idata...
[2025-08-04 13:01:16] [INFO] API: API调用成功，返回状态: success
[2025-08-04 13:01:16] [INFO] API: API返回消息: 获取商品列表成功
[2025-08-04 13:01:16] [INFO] API: 最终返回结果状态: success
[2025-08-04 13:01:16] [INFO] API: 最终返回结果: {"status":"success","message":"获取商品列表成功","data":[{"product_id":"73","merchant_id":"8034567958","product_name":"高仿情报局 同款数据","product_price":"48.80","product_description":"https:\/\/t.me\/idatas8\/28","stock_quantity":"12","status":"active","created_at":"2025-07-28 19:42:08","updated_at":"2025-07-28 19:44:09"},{"product_id":"72","merchant_id":"8034567958","product_name":"扣扣二合一\/不限","product_price":"258.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"18","status":"active","created_at":"2025-07-28 19:38:15","updated_at":"2025-07-28 19:39:07"},{"product_id":"71","merchant_id":"8034567958","product_name":"扣扣二合一\/单次","product_price":"25.00","product_description":"https:\/\/t.me\/idatas8\/322","stock_quantity":"30","status":"active","created_at":"2025-07-28 19:36:16","updated_at":"2025-07-28 19:37:44"},{"product_id":"68","merchant_id":"8034567958","product_name":"DA查询工具\/8次","product_price":"13.14","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"54","status":"active","created_at":"2025-07-25 17:46:13","updated_at":"2025-07-30 11:58:03"},{"product_id":"65","merchant_id":"8034567958","product_name":"DA查询工具\/不限次数","product_price":"88.00","product_description":"\n😏  -提供参数: 名字 身份证\n\n🌐 -返回参数: 户籍地址 名字 身份证 个人档案照(带证件照)\n\n下单后！\n前往 https:\/\/api.qnm6.top\/index.html 使用","stock_quantity":"30","status":"active","created_at":"2025-07-25 17:31:33","updated_at":"2025-07-25 17:42:40"},{"product_id":"27","merchant_id":"8034567958","product_name":"iDatas-9999天无限会员卡","product_price":"167.99","product_description":"\n永久会员  暑期限时优惠167.99\n永久无限制会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:40:04","updated_at":"2025-07-01 20:06:43"},{"product_id":"26","merchant_id":"8034567958","product_name":"iDatas-365天年度会员卡","product_price":"113.23","product_description":"\n年度会员 暑期限时优惠113.23\n365天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"50","status":"active","created_at":"2025-07-01 18:39:14","updated_at":"2025-07-01 18:40:33"},{"product_id":"25","merchant_id":"8034567958","product_name":"iDatas-90天季度会员卡","product_price":"33.44","product_description":"\n季卡会员 暑期限时优惠33.44\n90天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"41","status":"active","created_at":"2025-07-01 18:37:02","updated_at":"2025-08-02 12:09:26"},{"product_id":"24","merchant_id":"8034567958","product_name":"iDatas-30天月度会员卡","product_price":"13.14","product_description":"\n月卡会员暑期限时优惠13.14\n30天会员卡密-按需下单 按需购卡\n月卡享受20%数据  季卡享受50%数据\n年卡享受80%数据 永久享受100%数据\n","stock_quantity":"24","status":"active","created_at":"2025-07-01 18:33:41","updated_at":"2025-08-03 02:17:19"},{"product_id":"23","merchant_id":"8034567958","product_name":"iDatas-7天体验卡","product_price":"1.00","product_description":"iDatas 7天体验会员卡\r\n享受七天功能无限制使用","stock_quantity":"8","status":"active","created_at":"2025-07-01 18:29:29","updated_at":"2025-08-04 12:55:26"}]}
[2025-08-04 13:01:16] [INFO] API: === API请求处理完成 ===
