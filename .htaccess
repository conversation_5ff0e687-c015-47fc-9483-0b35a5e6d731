# 启用重写引擎
RewriteEngine On

# 修复 "No input file specified" 错误
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.php [QSA,L]

# 确保直接访问PHP文件
RewriteCond %{REQUEST_FILENAME} -f
RewriteCond %{REQUEST_FILENAME} \.php$
RewriteRule ^(.*)$ $1 [L]

# PHP设置 - 修复CGI问题
php_flag display_errors On
php_value error_reporting "E_ALL"
php_value log_errors On
php_value error_log "error.log"
php_value cgi.fix_pathinfo 1

# 文件类型设置
AddType application/x-httpd-php .php
AddHandler application/x-httpd-php .php

# 安全设置
Options -Indexes +FollowSymLinks
ServerSignature Off

# 防止直接访问敏感文件
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>