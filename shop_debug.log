[2025-08-04 12:56:32] [INFO] === 新的访问开始 ===
[2025-08-04 12:56:32] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:56:32] [INFO] 请求方法: GET
[2025-08-04 12:56:32] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:56:32] [INFO] POST参数: []
[2025-08-04 12:56:32] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:56:32] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 12:56:32] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:56:32] [INFO] 页面类型: merchant
[2025-08-04 12:56:32] [INFO] 商户ID: '8034567958'
[2025-08-04 12:56:32] [INFO] 商品ID: ''
[2025-08-04 12:59:50] [INFO] === 新的访问开始 ===
[2025-08-04 12:59:50] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 12:59:50] [INFO] 请求方法: GET
[2025-08-04 12:59:50] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 12:59:50] [INFO] POST参数: []
[2025-08-04 12:59:50] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 12:59:50] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 12:59:50] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 12:59:50] [INFO] 页面类型: merchant
[2025-08-04 12:59:50] [INFO] 商户ID: '8034567958'
[2025-08-04 12:59:50] [INFO] 商品ID: ''
[2025-08-04 13:00:05] [INFO] === 新的访问开始 ===
[2025-08-04 13:00:05] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:00:05] [INFO] 请求方法: GET
[2025-08-04 13:00:05] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:00:05] [INFO] POST参数: []
[2025-08-04 13:00:05] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:05] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:00:05] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 13:00:05] [INFO] 页面类型: merchant
[2025-08-04 13:00:05] [INFO] 商户ID: '8034567958'
[2025-08-04 13:00:05] [INFO] 商品ID: ''
[2025-08-04 13:00:37] [INFO] === 新的访问开始 ===
[2025-08-04 13:00:37] [INFO] 请求URI: /shop.php?sp=8034567958
[2025-08-04 13:00:37] [INFO] 请求方法: GET
[2025-08-04 13:00:37] [INFO] GET参数: {"sp":"8034567958"}
[2025-08-04 13:00:37] [INFO] POST参数: []
[2025-08-04 13:00:37] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:37] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:00:37] [INFO] 解析参数 - sj: '', sp: '8034567958'
[2025-08-04 13:00:37] [INFO] 页面类型: product
[2025-08-04 13:00:37] [INFO] 商户ID: ''
[2025-08-04 13:00:37] [INFO] 商品ID: '8034567958'
[2025-08-04 13:00:42] [INFO] === 新的访问开始 ===
[2025-08-04 13:00:42] [INFO] 请求URI: /shop.php?sp=73
[2025-08-04 13:00:42] [INFO] 请求方法: GET
[2025-08-04 13:00:42] [INFO] GET参数: {"sp":"73"}
[2025-08-04 13:00:42] [INFO] POST参数: []
[2025-08-04 13:00:42] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:00:42] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:00:42] [INFO] 解析参数 - sj: '', sp: '73'
[2025-08-04 13:00:42] [INFO] 页面类型: product
[2025-08-04 13:00:42] [INFO] 商户ID: ''
[2025-08-04 13:00:42] [INFO] 商品ID: '73'
[2025-08-04 13:01:15] [INFO] === 新的访问开始 ===
[2025-08-04 13:01:15] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:01:15] [INFO] 请求方法: GET
[2025-08-04 13:01:15] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:01:15] [INFO] POST参数: []
[2025-08-04 13:01:15] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:01:15] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:01:15] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 13:01:15] [INFO] 页面类型: merchant
[2025-08-04 13:01:15] [INFO] 商户ID: '8034567958'
[2025-08-04 13:01:15] [INFO] 商品ID: ''
[2025-08-04 13:05:43] [INFO] === 新的访问开始 ===
[2025-08-04 13:05:43] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:05:43] [INFO] 请求方法: GET
[2025-08-04 13:05:43] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:05:43] [INFO] POST参数: []
[2025-08-04 13:05:43] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:05:43] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:05:43] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 13:05:43] [INFO] 页面类型: merchant
[2025-08-04 13:05:43] [INFO] 商户ID: '8034567958'
[2025-08-04 13:05:43] [INFO] 商品ID: ''
[2025-08-04 13:07:55] [INFO] === 新的访问开始 ===
[2025-08-04 13:07:55] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:07:55] [INFO] 请求方法: GET
[2025-08-04 13:07:55] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:07:55] [INFO] POST参数: []
[2025-08-04 13:07:55] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:07:55] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:07:55] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 13:07:55] [INFO] 页面类型: merchant
[2025-08-04 13:07:55] [INFO] 商户ID: '8034567958'
[2025-08-04 13:07:55] [INFO] 商品ID: ''
[2025-08-04 13:17:02] [INFO] === 新的访问开始 ===
[2025-08-04 13:17:02] [INFO] 请求URI: /shop.php?sj=8034567958
[2025-08-04 13:17:02] [INFO] 请求方法: GET
[2025-08-04 13:17:02] [INFO] GET参数: {"sj":"8034567958"}
[2025-08-04 13:17:02] [INFO] POST参数: []
[2025-08-04 13:17:02] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:17:02] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:17:02] [INFO] 解析参数 - sj: '8034567958', sp: ''
[2025-08-04 13:17:02] [INFO] 页面类型: merchant
[2025-08-04 13:17:02] [INFO] 商户ID: '8034567958'
[2025-08-04 13:17:02] [INFO] 商品ID: ''
[2025-08-04 13:19:29] [INFO] === 新的访问开始 ===
[2025-08-04 13:19:29] [INFO] 请求URI: /shop.php?dd=ORDER17331234567890
[2025-08-04 13:19:29] [INFO] 请求方法: GET
[2025-08-04 13:19:29] [INFO] GET参数: {"dd":"ORDER17331234567890"}
[2025-08-04 13:19:29] [INFO] POST参数: []
[2025-08-04 13:19:29] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:19:29] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:19:29] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17331234567890'
[2025-08-04 13:19:29] [INFO] 页面类型: order
[2025-08-04 13:19:29] [INFO] 商户ID: ''
[2025-08-04 13:19:29] [INFO] 商品ID: ''
[2025-08-04 13:19:29] [INFO] 订单ID: 'ORDER17331234567890'
[2025-08-04 13:19:39] [INFO] === 新的访问开始 ===
[2025-08-04 13:19:39] [INFO] 请求URI: /shop.php?dd=ORDER17542839568146
[2025-08-04 13:19:39] [INFO] 请求方法: GET
[2025-08-04 13:19:39] [INFO] GET参数: {"dd":"ORDER17542839568146"}
[2025-08-04 13:19:39] [INFO] POST参数: []
[2025-08-04 13:19:39] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:19:39] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:19:39] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17542839568146'
[2025-08-04 13:19:39] [INFO] 页面类型: order
[2025-08-04 13:19:39] [INFO] 商户ID: ''
[2025-08-04 13:19:39] [INFO] 商品ID: ''
[2025-08-04 13:19:39] [INFO] 订单ID: 'ORDER17542839568146'
[2025-08-04 13:19:51] [INFO] === 新的访问开始 ===
[2025-08-04 13:19:51] [INFO] 请求URI: /shop.php
[2025-08-04 13:19:51] [INFO] 请求方法: GET
[2025-08-04 13:19:51] [INFO] GET参数: []
[2025-08-04 13:19:51] [INFO] POST参数: []
[2025-08-04 13:19:51] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:19:51] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:19:51] [INFO] 解析参数 - sj: '', sp: '', dd: ''
[2025-08-04 13:19:51] [ERROR] 参数验证失败：sj、sp和dd都为空，返回404
[2025-08-04 13:20:46] [INFO] === 新的访问开始 ===
[2025-08-04 13:20:46] [INFO] 请求URI: /shop.php?dd=ORDER17331234567890
[2025-08-04 13:20:46] [INFO] 请求方法: GET
[2025-08-04 13:20:46] [INFO] GET参数: {"dd":"ORDER17331234567890"}
[2025-08-04 13:20:46] [INFO] POST参数: []
[2025-08-04 13:20:46] [INFO] User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
[2025-08-04 13:20:46] [INFO] 客户端IP: 127.0.0.1
[2025-08-04 13:20:46] [INFO] 解析参数 - sj: '', sp: '', dd: 'ORDER17331234567890'
[2025-08-04 13:20:46] [INFO] 页面类型: order
[2025-08-04 13:20:46] [INFO] 商户ID: ''
[2025-08-04 13:20:46] [INFO] 商品ID: ''
[2025-08-04 13:20:46] [INFO] 订单ID: 'ORDER17331234567890'
