<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop页面测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #F2F2F7;
            color: #000;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .test-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #007AFF;
        }
        
        .test-link {
            display: block;
            background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 12px;
            margin-bottom: 12px;
            transition: transform 0.2s;
        }
        
        .test-link:hover {
            transform: translateY(-2px);
        }
        
        .test-description {
            color: #8E8E93;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .order-form {
            background: #F8F9FA;
            padding: 20px;
            border-radius: 12px;
            margin-top: 16px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            margin-bottom: 8px;
            color: #000;
        }
        
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #C6C6C8;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .form-button {
            background: linear-gradient(135deg, #FF9500 0%, #FFCC02 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .form-button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 40px; font-size: 28px; font-weight: 700;">Shop页面功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">🏪 商家页面测试</div>
            <div class="test-description">访问商家店铺，查看商家信息和商品列表</div>
            <a href="shop.php?sj=8034567958" target="_blank" class="test-link">
                访问商家页面 (sj=8034567958)
            </a>
        </div>
        
        <div class="test-section">
            <div class="test-title">📦 商品页面测试</div>
            <div class="test-description">直接访问商品详情页面，可以进行购买操作</div>
            <a href="shop.php?sp=73" target="_blank" class="test-link">
                访问商品页面 (sp=73)
            </a>
            <a href="shop.php?sp=74" target="_blank" class="test-link">
                访问商品页面 (sp=74)
            </a>
        </div>
        
        <div class="test-section">
            <div class="test-title">🔍 订单检测测试</div>
            <div class="test-description">检测订单支付状态，与"我已支付"按钮功能相同</div>
            
            <!-- 预设订单测试 -->
            <a href="shop.php?dd=ORDER17331234567890" target="_blank" class="test-link">
                测试订单检测 (dd=ORDER17331234567890)
            </a>
            
            <!-- 自定义订单号输入 -->
            <div class="order-form">
                <div class="form-group">
                    <label class="form-label" for="orderInput">输入订单号进行检测：</label>
                    <input type="text" id="orderInput" class="form-input" placeholder="请输入订单号，例如：ORDER17331234567890">
                </div>
                <button class="form-button" onclick="checkCustomOrder()">🔍 检测订单</button>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">📊 API测试</div>
            <div class="test-description">测试各种API接口</div>
            <a href="api_proxy.php?action=get_merchant_info&merchant_id=8034567958" target="_blank" class="test-link">
                测试商户信息API
            </a>
            <a href="api_proxy.php?action=get_product_list&merchant_id=8034567958" target="_blank" class="test-link">
                测试商品列表API
            </a>
            <a href="api_proxy.php?action=get_product_info&product_id=73" target="_blank" class="test-link">
                测试商品信息API
            </a>
        </div>
        
        <div class="test-section">
            <div class="test-title">📝 日志查看</div>
            <div class="test-description">查看系统运行日志和调试信息</div>
            <a href="logs.php" target="_blank" class="test-link">
                查看系统日志
            </a>
        </div>
        
        <div class="test-section">
            <div class="test-title">🎨 样式预览</div>
            <div class="test-description">查看iOS风格设计系统</div>
            <a href="style_preview.html" target="_blank" class="test-link">
                查看样式预览
            </a>
        </div>
    </div>
    
    <script>
        function checkCustomOrder() {
            const orderInput = document.getElementById('orderInput');
            const orderId = orderInput.value.trim();
            
            if (!orderId) {
                alert('请输入订单号');
                return;
            }
            
            // 打开新窗口检测订单
            window.open(`shop.php?dd=${encodeURIComponent(orderId)}`, '_blank');
        }
        
        // 回车键检测订单
        document.getElementById('orderInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                checkCustomOrder();
            }
        });
    </script>
</body>
</html>
