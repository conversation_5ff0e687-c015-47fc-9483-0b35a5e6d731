<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
</head>
<body>
    <h1>测试页面</h1>
    <p>如果您能看到这个页面，说明Web服务器正在运行。</p>
    
    <h2>测试链接</h2>
    <ul>
        <li><a href="phpinfo.php">PHP信息</a></li>
        <li><a href="debug.php">PHP调试</a></li>
        <li><a href="test.php">完整测试</a></li>
        <li><a href="shop_simple.php?sj=8034567958">简化商店页面</a></li>
        <li><a href="shop.php?sj=8034567958">完整商店页面</a></li>
        <li><a href="api_proxy.php?action=get_merchant_info&merchant_id=8034567958">API代理测试</a></li>
    </ul>
    
    <h2>JavaScript API测试</h2>
    <button onclick="testAPI()">测试API调用</button>
    <div id="result"></div>
    
    <script>
    function testAPI() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '测试中...';
        
        fetch('api_proxy.php?action=get_merchant_info&merchant_id=8034567958')
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(data => {
                console.log('Response data:', data);
                resultDiv.innerHTML = '<h3>API响应:</h3><pre>' + data + '</pre>';
            })
            .catch(error => {
                console.error('API错误:', error);
                resultDiv.innerHTML = '<h3>API错误:</h3><pre>' + error + '</pre>';
            });
    }
    </script>
</body>
</html>
