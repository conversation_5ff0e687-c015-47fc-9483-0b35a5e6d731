<?php
echo "PHP Debug Page\n";
echo "Time: " . date('Y-m-d H:i:s') . "\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Script: " . __FILE__ . "\n";
echo "Directory: " . __DIR__ . "\n";
echo "Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "\n";
echo "Query String: " . ($_SERVER['QUERY_STRING'] ?? 'N/A') . "\n";
echo "GET Parameters: " . json_encode($_GET) . "\n";

// 测试文件写入
$testFile = __DIR__ . '/debug_test.log';
$result = file_put_contents($testFile, "Test write: " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
echo "File write test: " . ($result !== false ? "SUCCESS" : "FAILED") . "\n";

// 测试外部API调用
echo "\nTesting external API call...\n";
$url = 'https://cloudshop.qnm6.top/get_merchant_info.php?merchant_id=8034567958';
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'header' => 'User-Agent: PHP-Debug/1.0'
    ]
]);

$response = @file_get_contents($url, false, $context);
if ($response === false) {
    $error = error_get_last();
    echo "API call FAILED: " . ($error['message'] ?? 'Unknown error') . "\n";
} else {
    echo "API call SUCCESS\n";
    echo "Response length: " . strlen($response) . "\n";
    echo "Response preview: " . substr($response, 0, 200) . "\n";
}
?>
